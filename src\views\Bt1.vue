<template>
  <div class="Bt1">
    <div id="pieChart" class="pie-chart"></div>
    <div class="Bt1Img1"></div>
    <div class="Bt1Img2"></div>
    <div class="Bt1Img3"></div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { onMounted, onUnmounted, computed, watch } from "vue";
import { usedata } from "../store/data";
const dataStore = usedata();

let myChart = null;

// 计算属性：从 dataStore 获取动态数据
const chartData = computed(() => {
  const alarmRisk = dataStore.data?.alarmRisk;
  if (!alarmRisk) {
    // 如果数据还未加载，返回默认值
    return [
      { value: 0, name: "高危攻击", itemStyle: { color: "#FD9391" } },
      { value: 0, name: "低危攻击", itemStyle: { color: "#32FEFC" } },
      { value: 0, name: "中危攻击", itemStyle: { color: "#95C1FA" } },
    ];
  }

  return [
    { value: alarmRisk.highNum || 0, name: "高危攻击", itemStyle: { color: "#FD9391" } },
    { value: alarmRisk.lowNum || 0, name: "低危攻击", itemStyle: { color: "#32FEFC" } },
    { value: alarmRisk.middleNum || 0, name: "中危攻击", itemStyle: { color: "#95C1FA" } },
  ];
});

const initChart = () => {
  const chartDom = document.getElementById("pieChart");
  if (chartDom) {
    myChart = echarts.init(chartDom);

    // 设置初始透明度为0，准备动画
    chartDom.style.opacity = "0";
    chartDom.style.transform = "scale(0.8)";
    chartDom.style.transition = "opacity 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)";

    updateChart();

    // 延迟启动入场动画
    setTimeout(() => {
      chartDom.style.opacity = "1";
      chartDom.style.transform = "scale(1)";
    }, 100);

    // 监听窗口大小变化
    window.addEventListener("resize", () => {
      myChart && myChart.resize();
    });
  }
};

const updateChart = () => {
  if (!myChart) return;

  const option = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },
    // 全局动画配置
    animation: true,
    animationDuration: 1500,
    animationEasing: "cubicOut",
    animationDelay: function (idx) {
      return idx * 200; // 每个扇形延迟200ms
    },
    series: [
      {
        name: "数据统计",
        type: "pie",
        radius: ["30%", "36%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        // 扇形动画配置
        animationType: "scale",
        animationEasing: "elasticOut",
        animationDelay: function (idx) {
          return idx * 150;
        },
        itemStyle: {
          borderRadius: 0,
          shadowBlur: 10,
          shadowColor: "rgba(0, 0, 0, 0.3)",
          shadowOffsetX: 2,
          shadowOffsetY: 2,
        },
        label: {
          show: true,
          position: "outside",
          fontSize: 12,
          color: "#ffffff",
          formatter: function (params) {
            return `{name|${params.name}}\n{value|${params.value}}`;
          },
          fontWeight: "normal",
          // 标签动画配置
          animation: true,
          animationDuration: 800,
          animationDelay: function (idx) {
            return 1000 + idx * 200; // 在扇形动画后延迟显示
          },
          animationEasing: "cubicOut",
          rich: {
            name: {
              fontSize: 12,
              color: "#a1a1a1",
              fontWeight: "normal",
              lineHeight: 16,
              align: "center",
            },
            value: {
              fontSize: 20,
              color: "#ffffff",
              fontWeight: "bold",
              lineHeight: 20,
              align: "center",
              padding: [18, 0, 0, 0],
            },
          },
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowColor: "rgba(50, 254, 252, 0.6)",
            shadowOffsetX: 0,
            shadowOffsetY: 0,
          },
          label: {
            show: true,
            fontSize: 14,
            fontWeight: "bold",
            formatter: function (params) {
              return `{name|${params.name}}\n{value|${params.value}}`;
            },
            rich: {
              name: {
                fontSize: 14,
                color: "#ffffff",
                fontWeight: "bold",
                lineHeight: 18,
                align: "center",
              },
              value: {
                fontSize: 16,
                color: "#32FEFC",
                fontWeight: "bold",
                lineHeight: 22,
                align: "center",
                padding: [8, 0, 0, 0],
              },
            },
          },
        },
        labelLine: {
          show: true,
          length: 20,
          length2: 20,
          // 连接线动画配置
          animation: true,
          animationDuration: 600,
          animationDelay: function (idx) {
            return 1200 + idx * 150; // 在标签动画后显示
          },
          lineStyle: {
            width: 1,
            color: "#ffffff",
            shadowBlur: 3,
            shadowColor: "rgba(255, 255, 255, 0.3)",
          },
        },
        data: chartData.value,
      },
    ],
  };

  myChart.setOption(option);
};

// 带动画的数据更新函数
const updateChartWithAnimation = () => {
  if (!myChart) return;

  // 先设置较短的动画时长用于数据更新
  const updateOption = {
    animation: true,
    animationDuration: 400,
    animationEasing: "cubicOut",
    series: [
      {
        data: chartData.value,
        animationDuration: 400,
        animationDelay: function (idx) {
          return idx * 50; // 数据更新时较短的延迟
        },
      },
    ],
  };

  myChart.setOption(updateOption);
};

// 监听数据变化，自动更新图表
watch(
  chartData,
  (_, oldData) => {
    // 如果是初始加载，使用完整动画
    if (!oldData || oldData.every((item) => item.value === 0)) {
      updateChart();
    } else {
      // 数据更新时使用较短动画
      updateChartWithAnimation();
    }
  },
  { deep: true }
);

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
  window.removeEventListener("resize", () => {
    myChart && myChart.resize();
  });
});
</script>

<style lang="less" scoped>
.Bt1 {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  pointer-events: all;
}

.pie-chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
  filter: drop-shadow(0 0 20px rgba(50, 254, 252, 0.3));
  transition: filter 0.3s ease;
}

.pie-chart:hover {
  filter: drop-shadow(0 0 30px rgba(50, 254, 252, 0.5));
}
.Bt1Img1 {
  position: absolute;
  width: 58%;
  left: 97px;
  height: 118%;
  background-image: url("../../public/img/w.png");
  background-repeat: no-repeat;
  background-size: contain;
  pointer-events: none;
}
.Bt1Img2 {
  position: absolute;
  width: 49%;
  height: 117%;
  background-image: url("../../public/img/w2.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  animation: rotate-medium 15s linear infinite;
  pointer-events: none;
}
.Bt1Img3 {
  position: absolute;
  width: 43%;
  height: 107%;
  background-image: url("../../public/img/w3.png");
  background-repeat: no-repeat;
  background-size: contain;
  animation: rotate-fast 10s linear infinite;
  pointer-events: none;
}

@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate-medium {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate-fast {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}
</style>
